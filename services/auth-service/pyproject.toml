[tool.poetry]
name = "auth-service"
version = "0.1.0"
description = "Authentication and authorization microservice for Cortexa"
authors = ["Cortexa Team"]
readme = "README.md"
packages = [{include = "src"}]

[tool.poetry.dependencies]
python = "^3.11"
cortexa-common = {path = "../../packages/cortexa-common", develop = true}

[tool.poetry.group.dev.dependencies]
pytest = "^7.4.3"
pytest-asyncio = "^0.21.1"
pytest-cov = "^4.1.0"
httpx = "^0.25.2"

[tool.ruff]
target-version = "py311"
line-length = 88
extend = "../../packages/cortexa-common/pyproject.toml"

[tool.mypy]
python_version = "3.11"
extend = "../../packages/cortexa-common/pyproject.toml"

[build-system]
requires = ["poetry-core"]
build-backend = "poetry.core.masonry.api"
