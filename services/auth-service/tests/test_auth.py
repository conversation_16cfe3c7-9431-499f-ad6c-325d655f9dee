"""Tests for authentication endpoints."""

import pytest
from httpx import AsyncClient


@pytest.mark.asyncio
async def test_health_check(client: AsyncClient):
    """Test health check endpoint."""
    response = await client.get("/health")
    assert response.status_code == 200
    data = response.json()
    assert data["status"] == "healthy"
    assert data["service"] == "auth-service"


@pytest.mark.asyncio
async def test_login_invalid_credentials(client: AsyncClient):
    """Test login with invalid credentials."""
    response = await client.post(
        "/api/v1/auth/login",
        json={
            "email": "<EMAIL>",
            "password": "wrongpassword"
        }
    )
    assert response.status_code == 401
    data = response.json()
    assert "Incorrect email or password" in data["detail"]


@pytest.mark.asyncio
async def test_register_requires_admin(client: AsyncClient):
    """Test that user registration requires admin role."""
    response = await client.post(
        "/api/v1/auth/register",
        json={
            "email": "<EMAIL>",
            "password": "testpassword123",
            "role": "OPERATOR"
        }
    )
    # Should fail due to missing authentication
    assert response.status_code == 401
