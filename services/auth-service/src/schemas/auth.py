from datetime import datetime
from typing import Optional

from pydantic import EmailStr, Field

from cortexacommon.schemas import BaseSchema, TimestampMixin, UUIDMixin


class UserLogin(BaseSchema):
    """User login request schema."""
    
    email: EmailStr = Field(description="User email address")
    password: str = Field(min_length=8, description="User password")


class UserCreate(BaseSchema):
    """User creation schema."""
    
    email: EmailStr = Field(description="User email address")
    password: str = Field(min_length=8, description="User password")
    role: str = Field(default="OPERATOR", description="User role")
    first_name: Optional[str] = Field(default=None, max_length=100, description="First name")
    last_name: Optional[str] = Field(default=None, max_length=100, description="Last name")


class UserUpdate(BaseSchema):
    """User update schema."""
    
    email: Optional[EmailStr] = Field(default=None, description="User email address")
    role: Optional[str] = Field(default=None, description="User role")
    first_name: Optional[str] = Field(default=None, max_length=100, description="First name")
    last_name: Optional[str] = Field(default=None, max_length=100, description="Last name")
    is_active: Optional[bool] = Field(default=None, description="User active status")


class UserResponse(BaseSchema, UUIDMixin, TimestampMixin):
    """User response schema."""
    
    email: EmailStr = Field(description="User email address")
    role: str = Field(description="User role")
    is_active: bool = Field(description="User active status")
    first_name: Optional[str] = Field(description="First name")
    last_name: Optional[str] = Field(description="Last name")
    last_login: Optional[datetime] = Field(description="Last login timestamp")


class TokenResponse(BaseSchema):
    """Token response schema."""
    
    access_token: str = Field(description="JWT access token")
    refresh_token: str = Field(description="JWT refresh token")
    token_type: str = Field(default="bearer", description="Token type")
    expires_in: int = Field(description="Token expiration time in seconds")


class RefreshTokenRequest(BaseSchema):
    """Refresh token request schema."""
    
    refresh_token: str = Field(description="Refresh token")

