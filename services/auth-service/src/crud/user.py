"""User CRUD operations."""

from datetime import datetime
from typing import <PERSON><PERSON>
from uuid import UUI<PERSON>

from sqlalchemy import select, update
from sqlalchemy.ext.asyncio import AsyncSession

from cortexacommon.db import BaseRepository
from cortexacommon.security import get_password_hash, verify_password
from ..db.models import User, RefreshToken
from ..schemas.auth import UserCreate, UserUpdate


class UserRepository(BaseRepository[User, UserCreate, UserUpdate]):
    """Repository for user operations."""
    
    def __init__(self, session: AsyncSession):
        super().__init__(User, session)
    
    async def get_by_email(self, email: str) -> Optional[User]:
        """Get user by email address."""
        result = await self.session.execute(
            select(User).where(User.email == email)
        )
        return result.scalar_one_or_none()
    
    async def create_user(self, user_create: UserCreate) -> User:
        """Create a new user with hashed password."""
        hashed_password = get_password_hash(user_create.password)
        
        user_data = user_create.model_dump(exclude={"password"})
        user_data["hashed_password"] = hashed_password
        
        db_user = User(**user_data)
        self.session.add(db_user)
        await self.session.commit()
        await self.session.refresh(db_user)
        return db_user
    
    async def authenticate(self, email: str, password: str) -> Optional[User]:
        """Authenticate user with email and password."""
        user = await self.get_by_email(email)
        if not user:
            return None
        
        if not verify_password(password, user.hashed_password):
            return None
        
        # Update last login
        await self.session.execute(
            update(User)
            .where(User.id == user.id)
            .values(last_login=datetime.utcnow())
        )
        await self.session.commit()
        
        return user
    
    async def change_password(self, user_id: UUID, new_password: str) -> bool:
        """Change user password."""
        hashed_password = get_password_hash(new_password)
        
        result = await self.session.execute(
            update(User)
            .where(User.id == user_id)
            .values(hashed_password=hashed_password)
        )
        await self.session.commit()
        return result.rowcount > 0


class RefreshTokenRepository(BaseRepository[RefreshToken, dict, dict]):
    """Repository for refresh token operations."""
    
    def __init__(self, session: AsyncSession):
        super().__init__(RefreshToken, session)
    
    async def create_refresh_token(
        self, 
        user_id: UUID, 
        token_hash: str, 
        expires_at: datetime
    ) -> RefreshToken:
        """Create a new refresh token."""
        refresh_token = RefreshToken(
            user_id=user_id,
            token_hash=token_hash,
            expires_at=expires_at
        )
        self.session.add(refresh_token)
        await self.session.commit()
        await self.session.refresh(refresh_token)
        return refresh_token
    
    async def get_by_token_hash(self, token_hash: str) -> Optional[RefreshToken]:
        """Get refresh token by hash."""
        result = await self.session.execute(
            select(RefreshToken).where(
                RefreshToken.token_hash == token_hash,
                RefreshToken.is_revoked == False,
                RefreshToken.expires_at > datetime.utcnow()
            )
        )
        return result.scalar_one_or_none()
    
    async def revoke_token(self, token_id: UUID) -> bool:
        """Revoke a refresh token."""
        result = await self.session.execute(
            update(RefreshToken)
            .where(RefreshToken.id == token_id)
            .values(is_revoked=True, revoked_at=datetime.utcnow())
        )
        await self.session.commit()
        return result.rowcount > 0
    
    async def revoke_all_user_tokens(self, user_id: UUID) -> int:
        """Revoke all refresh tokens for a user."""
        result = await self.session.execute(
            update(RefreshToken)
            .where(RefreshToken.user_id == user_id, RefreshToken.is_revoked == False)
            .values(is_revoked=True, revoked_at=datetime.utcnow())
        )
        await self.session.commit()
        return result.rowcount
