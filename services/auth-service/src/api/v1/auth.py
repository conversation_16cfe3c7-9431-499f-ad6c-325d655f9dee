"""Authentication API endpoints."""

from datetime import datetime, timedelta, timezone
from typing import Any

from fastapi import API<PERSON>outer, Depends, HTTPException, status
from sqlalchemy.ext.asyncio import AsyncSession

from cortexacommon.db import get_async_session
from cortexacommon.schemas import ResponseModel
from cortexacommon.security import (
    create_access_token,
    create_refresh_token,
    verify_token,
    get_password_hash,
    RoleChe<PERSON>,
)
from ...core.config import settings
from ...crud.user import UserRepository, RefreshTokenRepository
from ...schemas.auth import (
    UserLogin,
    UserCreate,
    UserResponse,
    TokenResponse,
    RefreshTokenRequest,
)

router = APIRouter()

# Role checker for admin operations
admin_required = RoleChecker(["ADMIN"])


@router.post("/login", response_model=ResponseModel[TokenResponse])
async def login(
    user_login: UserLogin,
    session: AsyncSession = Depends(get_async_session),
) -> ResponseModel[TokenResponse]:
    """Authenticate user and return tokens."""
    user_repo = UserRepository(session)
    refresh_token_repo = RefreshTokenRepository(session)
    
    # Authenticate user
    user = await user_repo.authenticate(user_login.email, user_login.password)
    if not user:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password"
        )
    
    if not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User account is disabled"
        )
    
    # Create tokens
    token_data = {"sub": str(user.id), "role": user.role}
    
    access_token = create_access_token(token_data, settings.security)
    refresh_token = create_refresh_token(token_data, settings.security)
    
    # Store refresh token hash
    refresh_token_hash = get_password_hash(refresh_token)
    expires_at = datetime.now(timezone.utc) + timedelta(days=settings.security.refresh_token_expire_days)
    
    await refresh_token_repo.create_refresh_token(
        user_id=user.id,
        token_hash=refresh_token_hash,
        expires_at=expires_at
    )
    
    token_response = TokenResponse(
        access_token=access_token,
        refresh_token=refresh_token,
        expires_in=settings.security.access_token_expire_minutes * 60
    )
    
    return ResponseModel(
        success=True,
        message="Login successful",
        data=token_response
    )


@router.post("/refresh", response_model=ResponseModel[TokenResponse])
async def refresh_token(
    refresh_request: RefreshTokenRequest,
    session: AsyncSession = Depends(get_async_session),
) -> ResponseModel[TokenResponse]:
    """Refresh access token using refresh token."""
    refresh_token_repo = RefreshTokenRepository(session)
    user_repo = UserRepository(session)
    
    # Verify refresh token
    try:
        token_data = verify_token(
            refresh_request.refresh_token, 
            settings.security, 
            token_type="refresh"
        )
    except HTTPException:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid refresh token"
        )
    
    # Check if refresh token exists and is valid
    refresh_token_hash = get_password_hash(refresh_request.refresh_token)
    stored_token = await refresh_token_repo.get_by_token_hash(refresh_token_hash)
    
    if not stored_token:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Refresh token not found or expired"
        )
    
    # Get user
    user = await user_repo.get(token_data.user_id)
    if not user or not user.is_active:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="User not found or inactive"
        )
    
    # Create new access token
    new_token_data = {"sub": str(user.id), "role": user.role}
    access_token = create_access_token(new_token_data, settings.security)
    
    token_response = TokenResponse(
        access_token=access_token,
        refresh_token=refresh_request.refresh_token,  # Keep same refresh token
        expires_in=settings.security.access_token_expire_minutes * 60
    )
    
    return ResponseModel(
        success=True,
        message="Token refreshed successfully",
        data=token_response
    )


@router.post("/register", response_model=ResponseModel[UserResponse])
async def register(
    user_create: UserCreate,
    session: AsyncSession = Depends(get_async_session),
    _: Any = Depends(admin_required),  # Only admins can create users
) -> ResponseModel[UserResponse]:
    """Register a new user (admin only)."""
    user_repo = UserRepository(session)
    
    # Check if user already exists
    existing_user = await user_repo.get_by_email(user_create.email)
    if existing_user:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="User with this email already exists"
        )
    
    # Create user
    user = await user_repo.create_user(user_create)
    user_response = UserResponse.model_validate(user)
    
    return ResponseModel(
        success=True,
        message="User created successfully",
        data=user_response
    )
