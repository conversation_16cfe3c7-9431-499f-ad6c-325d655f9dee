# Cortexa Microservices Platform

A comprehensive microservices architecture for call data management and voice processing, built with FastAPI, PostgreSQL, and Kafka.

## Architecture Overview

This platform implements a domain-driven microservices architecture with the following core services:

- **Auth Service**: Authentication and authorization with JWT tokens and RBAC
- **Call Data Service**: Call metadata and transcript management
- **Voice Gateway**: Voice processing and real-time communication (planned)
- **Archiving Service**: Data archiving and retention management (planned)

## Phase 1: Foundational Setup ✅

### Completed Features

- **Docker Compose Infrastructure**: PostgreSQL, Kafka, Redis, and Kafka UI
- **Common Package**: Shared utilities, database layer, security, and configuration
- **Code Quality Tools**: Ruff linter/formatter with pre-commit hooks
- **Standard Microservice Template**: Domain-driven project structure
- **Configuration Management**: Pydantic BaseSettings with environment variables

### Technology Stack

- **Language**: Python 3.11+
- **Framework**: FastAPI with async/await
- **Database**: PostgreSQL with SQLAlchemy 2.0 (async)
- **Message Queue**: Apache Kafka with aiokafka
- **Caching**: Redis
- **Authentication**: JWT with python-jose
- **Dependency Management**: Poetry
- **Code Quality**: Ruff, MyPy, Pre-commit hooks
- **Containerization**: Docker with multi-stage builds

## Phase 2: Core Services ✅

### Auth Service

**Port**: 8001
**Endpoints**:
- `POST /api/v1/auth/login` - User authentication
- `POST /api/v1/auth/refresh` - Token refresh
- `POST /api/v1/auth/register` - User registration (admin only)

**Features**:
- JWT access tokens (30 min expiry)
- Refresh tokens (7 day expiry)
- Role-based access control (OPERATOR, MANAGER, TRANSLATOR, ADMIN)
- Password hashing with bcrypt
- Token blacklisting support

### Call Data Service

**Port**: 8002
**Endpoints**:
- `POST /api/v1/calls/` - Create call record
- `GET /api/v1/calls/{id}` - Get call by ID
- `PUT /api/v1/calls/{id}` - Update call record
- `POST /api/v1/calls/search` - Search calls with filters
- `GET /api/v1/calls/stats/overview` - Call statistics (manager+)
- `POST /api/v1/calls/{id}/segments` - Add call segments
- `GET /api/v1/calls/{id}/segments` - Get call segments

**Features**:
- Call metadata storage (duration, quality, status)
- Full transcript storage with confidence scores
- Call segmentation support
- Advanced search and filtering
- Statistics and analytics
- Multi-language support

### Role-Based Access Control

The platform implements a comprehensive RBAC system:

- **OPERATOR**: Basic call data access
- **MANAGER**: Call statistics and advanced features
- **TRANSLATOR**: Translation-specific features (planned)
- **ADMIN**: Full system access including user management

## Quick Start

### Prerequisites

- Docker and Docker Compose
- Python 3.11+ (for local development)
- Poetry (for dependency management)

### 1. Start Infrastructure

```bash
# Start all services
docker-compose up -d

# Check service health
docker-compose ps
```

### 2. Install Dependencies (Local Development)

```bash
# Install common package
cd packages/cortexa-common
poetry install

# Install auth service
cd ../../services/auth-service
poetry install

# Install call data service
cd ../call-data-service
poetry install
```

### 3. Run Database Migrations

```bash
# Auth service migrations
cd services/auth-service
poetry run alembic upgrade head

# Call data service migrations
cd ../call-data-service
poetry run alembic upgrade head
```

### 4. Create Initial Admin User

```bash
# Use the auth service API to create an admin user
curl -X POST "http://localhost:8001/api/v1/auth/register" \
  -H "Content-Type: application/json" \
  -H "Authorization: Bearer <admin-token>" \
  -d '{
    "email": "<EMAIL>",
    "password": "secure-password",
    "role": "ADMIN",
    "first_name": "Admin",
    "last_name": "User"
  }'
```

## Development

### Code Quality

The project uses several tools to maintain code quality:

```bash
# Install pre-commit hooks
pre-commit install

# Run linting
ruff check .

# Run formatting
ruff format .

# Run type checking
mypy .
```

### Testing

```bash
# Run tests for a service
cd services/auth-service
poetry run pytest

# Run with coverage
poetry run pytest --cov=src --cov-report=html
```

### Adding a New Service

1. Create service directory: `services/new-service/`
2. Copy the standard template structure
3. Update `pyproject.toml` with service-specific settings
4. Implement your domain models, schemas, and API endpoints
5. Add service to `docker-compose.yml`
6. Create Alembic migrations if needed

## API Documentation

Each service provides interactive API documentation:

- Auth Service: http://localhost:8001/docs
- Call Data Service: http://localhost:8002/docs

## Monitoring and Management

- **Kafka UI**: http://localhost:8080 - Kafka topic and message management
- **PostgreSQL**: localhost:5432 - Database access
- **Redis**: localhost:6379 - Cache access

## Configuration

All services use environment variables for configuration:

### Database
- `DB_HOST`: Database host (default: localhost)
- `DB_PORT`: Database port (default: 5432)
- `DB_USER`: Database user (default: cortexa_user)
- `DB_PASSWORD`: Database password (default: cortexa_password)
- `DB_NAME`: Database name (default: cortexa)

### Security
- `SECURITY_SECRET_KEY`: JWT secret key (change in production!)
- `SECURITY_ACCESS_TOKEN_EXPIRE_MINUTES`: Access token expiry (default: 30)
- `SECURITY_REFRESH_TOKEN_EXPIRE_DAYS`: Refresh token expiry (default: 7)

### Kafka
- `KAFKA_BOOTSTRAP_SERVERS`: Kafka servers (default: localhost:9092)
- `KAFKA_GROUP_ID`: Consumer group ID (default: cortexa-group)

### Redis
- `REDIS_HOST`: Redis host (default: localhost)
- `REDIS_PORT`: Redis port (default: 6379)

## Next Steps

### Phase 3: Advanced Features (Planned)

- Voice Gateway service for real-time voice processing
- Translation service integration
- Advanced analytics and reporting
- Real-time notifications with WebSockets
- File storage service for audio recordings
- Monitoring and observability (Prometheus, Grafana)

### Production Considerations

- Use proper secrets management (HashiCorp Vault, AWS Secrets Manager)
- Implement proper logging and monitoring
- Set up CI/CD pipelines
- Configure load balancing and auto-scaling
- Implement backup and disaster recovery
- Security hardening and penetration testing

## Contributing

1. Follow the established code style and patterns
2. Write tests for new features
3. Update documentation
4. Use conventional commit messages
5. Ensure all pre-commit hooks pass

## License

[Add your license information here]