"""Authentication and authorization utilities."""

from datetime import datetime, timedelta
from typing import Any, Dict, List, Optional
from uuid import UUID

from fastapi import Depends, HTTPException, status
from fastapi.security import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, HTTPAuthorizationCredentials
from jose import J<PERSON><PERSON>rror, jwt
from passlib.context import <PERSON><PERSON><PERSON><PERSON>xt
from pydantic import BaseModel

from ..config import SecuritySettings


# Password hashing
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# HTTP Bearer token scheme
security = HTTPBearer()


class TokenData(BaseModel):
    """Token data model."""
    user_id: Optional[UUID] = None
    role: Optional[str] = None


class User(BaseModel):
    """User model for authentication."""
    id: UUID
    email: str
    role: str
    is_active: bool = True


def get_password_hash(password: str) -> str:
    """Hash a password."""
    return pwd_context.hash(password)


def verify_password(plain_password: str, hashed_password: str) -> bool:
    """Verify a password against its hash."""
    return pwd_context.verify(plain_password, hashed_password)


def create_access_token(
    data: Dict[str, Any], 
    security_settings: SecuritySettings,
    expires_delta: Optional[timedelta] = None
) -> str:
    """Create a JWT access token."""
    to_encode = data.copy()
    
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(
            minutes=security_settings.access_token_expire_minutes
        )
    
    to_encode.update({"exp": expire, "type": "access"})
    encoded_jwt = jwt.encode(
        to_encode, 
        security_settings.secret_key, 
        algorithm=security_settings.algorithm
    )
    return encoded_jwt


def create_refresh_token(
    data: Dict[str, Any], 
    security_settings: SecuritySettings,
    expires_delta: Optional[timedelta] = None
) -> str:
    """Create a JWT refresh token."""
    to_encode = data.copy()
    
    if expires_delta:
        expire = datetime.utcnow() + expires_delta
    else:
        expire = datetime.utcnow() + timedelta(
            days=security_settings.refresh_token_expire_days
        )
    
    to_encode.update({"exp": expire, "type": "refresh"})
    encoded_jwt = jwt.encode(
        to_encode, 
        security_settings.secret_key, 
        algorithm=security_settings.algorithm
    )
    return encoded_jwt


def verify_token(
    token: str, 
    security_settings: SecuritySettings,
    token_type: str = "access"
) -> TokenData:
    """Verify and decode a JWT token."""
    try:
        payload = jwt.decode(
            token, 
            security_settings.secret_key, 
            algorithms=[security_settings.algorithm]
        )
        
        # Check token type
        if payload.get("type") != token_type:
            raise JWTError("Invalid token type")
        
        user_id: str = payload.get("sub")
        role: str = payload.get("role")
        
        if user_id is None:
            raise JWTError("Token missing user ID")
        
        token_data = TokenData(user_id=UUID(user_id), role=role)
        return token_data
        
    except JWTError:
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Could not validate credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )


def get_security_settings() -> SecuritySettings:
    """Dependency to get security settings."""
    return SecuritySettings()


async def get_current_user(
    credentials: HTTPAuthorizationCredentials = Depends(security),
    security_settings: SecuritySettings = Depends(get_security_settings),
) -> TokenData:
    """Get the current user from the JWT token."""
    return verify_token(credentials.credentials, security_settings)


async def get_current_active_user(
    current_user: TokenData = Depends(get_current_user),
) -> TokenData:
    """Get the current active user."""
    # In a real implementation, you would check if the user is active
    # by querying the database. For now, we assume all users are active.
    return current_user


class RoleChecker:
    """Dependency to check if user has required roles."""
    
    def __init__(self, allowed_roles: List[str]):
        """Initialize with allowed roles."""
        self.allowed_roles = allowed_roles
    
    def __call__(self, current_user: TokenData = Depends(get_current_active_user)) -> TokenData:
        """Check if user has required role."""
        if current_user.role not in self.allowed_roles:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Operation not permitted"
            )
        return current_user
