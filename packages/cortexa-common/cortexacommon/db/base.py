"""Database base configuration and session management."""

from typing import Async<PERSON>enerator
from sqlalchemy.ext.asyncio import AsyncSession, async_sessionmaker, create_async_engine
from sqlalchemy.orm import DeclarativeBase
from sqlalchemy import MetaData

from ..config import DatabaseSettings


class Base(DeclarativeBase):
    """Base class for all SQLAlchemy models."""
    
    metadata = MetaData(
        naming_convention={
            "ix": "ix_%(column_0_label)s",
            "uq": "uq_%(table_name)s_%(column_0_name)s",
            "ck": "ck_%(table_name)s_%(constraint_name)s",
            "fk": "fk_%(table_name)s_%(column_0_name)s_%(referred_table_name)s",
            "pk": "pk_%(table_name)s",
        }
    )


# Global variables for database session management
async_engine = None
async_session_maker = None


def init_db(database_settings: DatabaseSettings) -> None:
    """Initialize the database engine and session maker."""
    global async_engine, async_session_maker
    
    async_engine = create_async_engine(
        database_settings.url,
        echo=False,  # Set to True for SQL query logging
        future=True,
        pool_pre_ping=True,
        pool_recycle=300,
    )
    
    async_session_maker = async_sessionmaker(
        async_engine,
        class_=AsyncSession,
        expire_on_commit=False,
    )


async def get_async_session() -> AsyncGenerator[AsyncSession, None]:
    """Get an async database session."""
    if async_session_maker is None:
        raise RuntimeError("Database not initialized. Call init_db() first.")
    
    async with async_session_maker() as session:
        try:
            yield session
        except Exception:
            await session.rollback()
            raise
        finally:
            await session.close()
