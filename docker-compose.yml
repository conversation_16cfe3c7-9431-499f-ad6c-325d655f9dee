version: '3.8'

services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: cortexa-postgres
    environment:
      POSTGRES_DB: cortexa
      POSTGRES_USER: cortexa_user
      POSTGRES_PASSWORD: cortexa_password
    ports:
      - "5432:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./scripts/init-db.sql:/docker-entrypoint-initdb.d/init-db.sql
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U cortexa_user -d cortexa"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - cortexa-network

  # Apache Kafka
  zookeeper:
    image: confluentinc/cp-zookeeper:7.4.0
    container_name: cortexa-zookeeper
    environment:
      ZOOKEEPER_CLIENT_PORT: 2181
      ZOOKEEPER_TICK_TIME: 2000
    networks:
      - cortexa-network

  kafka:
    image: confluentinc/cp-kafka:7.4.0
    container_name: cortexa-kafka
    depends_on:
      - zookeeper
    ports:
      - "9092:9092"
    environment:
      KAFKA_BROKER_ID: 1
      KAFKA_ZOOKEEPER_CONNECT: zookeeper:2181
      KAFKA_LISTENER_SECURITY_PROTOCOL_MAP: PLAINTEXT:PLAINTEXT,PLAINTEXT_HOST:PLAINTEXT
      KAFKA_ADVERTISED_LISTENERS: PLAINTEXT://kafka:29092,PLAINTEXT_HOST://localhost:9092
      KAFKA_OFFSETS_TOPIC_REPLICATION_FACTOR: 1
      KAFKA_TRANSACTION_STATE_LOG_MIN_ISR: 1
      KAFKA_TRANSACTION_STATE_LOG_REPLICATION_FACTOR: 1
      KAFKA_AUTO_CREATE_TOPICS_ENABLE: 'true'
    healthcheck:
      test: ["CMD", "kafka-topics", "--bootstrap-server", "localhost:9092", "--list"]
      interval: 30s
      timeout: 10s
      retries: 5
    networks:
      - cortexa-network

  # Redis for caching and session storage
  redis:
    image: redis:7-alpine
    container_name: cortexa-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - cortexa-network

  # Kafka UI for development
  kafka-ui:
    image: provectuslabs/kafka-ui:latest
    container_name: cortexa-kafka-ui
    depends_on:
      - kafka
    ports:
      - "8080:8080"
    environment:
      KAFKA_CLUSTERS_0_NAME: local
      KAFKA_CLUSTERS_0_BOOTSTRAPSERVERS: kafka:29092
    networks:
      - cortexa-network

volumes:
  postgres_data:
  redis_data:

  # Auth Service
  auth-service:
    build:
      context: ./services/auth-service
      dockerfile: Dockerfile
    container_name: cortexa-auth-service
    depends_on:
      postgres:
        condition: service_healthy
    ports:
      - "8001:8000"
    environment:
      DB_HOST: postgres
      DB_PORT: 5432
      DB_USER: cortexa_user
      DB_PASSWORD: cortexa_password
      DB_NAME: cortexa
      REDIS_HOST: redis
      KAFKA_BOOTSTRAP_SERVERS: kafka:29092
      SECURITY_SECRET_KEY: your-secret-key-change-in-production
    networks:
      - cortexa-network
    restart: unless-stopped


networks:
  cortexa-network:
    driver: bridge